{"projectTemplate": {"name": "AI Professional Project Template", "version": "1.0.0", "description": "قالب احترافي للمشاريع مع الذكاء الاصطناعي", "structure": {"folders": ["src", "tests", "docs", "config", "scripts", "assets", "data"], "files": {"README.md": "# {{projectName}}\n\n## 🎯 الهدف\n{{projectDescription}}\n\n## 🚀 البدء السريع\n```bash\n# تثبيت المتطلبات\npip install -r requirements.txt\n\n# تشغيل المشروع\npython src/main.py\n```\n\n## 📋 المميزات\n- [ ] الميزة الأولى\n- [ ] الميزة الثانية\n- [ ] الميزة الثالثة\n\n## 🧪 الاختبارات\n```bash\npytest tests/\n```\n\n## 📚 التوثيق\nيمكن العثور على التوثيق الكامل في مجلد `docs/`\n\n## 🤝 المساهمة\nنرحب بالمساهمات! يرجى قراءة `CONTRIBUTING.md` أولاً.\n\n## 📄 الرخصة\nهذا المشروع مرخص تحت رخصة MIT - انظر ملف `LICENSE` للتفاصيل.", "CHANGELOG.md": "# سجل التغييرات\n\n## [غير منشور]\n### أضيف\n- إعداد المشروع الأولي\n- إنشاء الهيكل الأساسي\n\n### تم تغييره\n- لا شيء حتى الآن\n\n### تم إصلاحه\n- لا شيء حتى الآن\n\n## [1.0.0] - {{currentDate}}\n### أضيف\n- الإصدار الأولي من المشروع", "CONTRIBUTING.md": "# دليل المساهمة\n\n## 🤝 كيفية المساهمة\n\n### 1. Fork المشروع\n### 2. إنشاء branch جديد\n```bash\ngit checkout -b feature/amazing-feature\n```\n\n### 3. Commit التغييرات\n```bash\ngit commit -m 'Add some amazing feature'\n```\n\n### 4. Push للـ branch\n```bash\ngit push origin feature/amazing-feature\n```\n\n### 5. فتح Pull Request\n\n## 📋 معايير الكود\n- استخدم أسماء واضحة للمتغيرات والدوال\n- اكتب تعليقات مفيدة\n- أضف اختبارات للكود الجديد\n- اتبع PEP 8 للـ Python\n\n## 🧪 الاختبارات\nتأكد من تشغيل جميع الاختبارات قبل إرسال PR:\n```bash\npytest tests/\n```", "requirements.txt": "# متطلبات Python الأساسية\npytest>=7.0.0\nblack>=22.0.0\npylint>=2.15.0\nrequests>=2.28.0\nnumpy>=1.21.0\npandas>=1.5.0", ".gitignore": "# Byte-compiled / optimized / DLL files\n__pycache__/\n*.py[cod]\n*$py.class\n\n# C extensions\n*.so\n\n# Distribution / packaging\n.Python\nbuild/\ndevelop-eggs/\ndist/\ndownloads/\neggs/\n.eggs/\nlib/\nlib64/\nparts/\nsdist/\nvar/\nwheels/\n*.egg-info/\n.installed.cfg\n*.egg\nPYMANIFEST\n\n# PyInstaller\n*.manifest\n*.spec\n\n# Installer logs\npip-log.txt\npip-delete-this-directory.txt\n\n# Unit test / coverage reports\nhtmlcov/\n.tox/\n.coverage\n.coverage.*\n.cache\nnosetests.xml\ncoverage.xml\n*.cover\n.hypothesis/\n.pytest_cache/\n\n# Translations\n*.mo\n*.pot\n\n# Django stuff:\n*.log\nlocal_settings.py\ndb.sqlite3\n\n# Flask stuff:\ninstance/\n.webassets-cache\n\n# Scrapy stuff:\n.scrapy\n\n# Sphinx documentation\ndocs/_build/\n\n# PyBuilder\ntarget/\n\n# Jupyter Notebook\n.ipynb_checkpoints\n\n# pyenv\n.python-version\n\n# celery beat schedule file\ncelerybeat-schedule\n\n# SageMath parsed files\n*.sage.py\n\n# Environments\n.env\n.venv\nenv/\nvenv/\nENV/\nenv.bak/\nvenv.bak/\n\n# Spyder project settings\n.spyderproject\n.spyproject\n\n# Rope project settings\n.ropeproject\n\n# mkdocs documentation\n/site\n\n# mypy\n.mypy_cache/\n.dmypy.json\ndmypy.json\n\n# VS Code\n.vscode/\n!.vscode/settings.json\n!.vscode/tasks.json\n!.vscode/launch.json\n!.vscode/extensions.json\n\n# Local History for Visual Studio Code\n.history/\n\n# Node.js\nnode_modules/\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*"}}, "aiInstructions": {"systemPrompt": "أنت وكيل ذكي محترف متخصص في تطوير البرمجيات. اتبع المعايير المحترفة في الكود والتوثيق والاختبارات.", "workflowSteps": ["قراءة وفهم السياق الكامل", "تحليل المتطلبات وتقسيمها لمهام صغيرة", "كتابة كود نظيف ومنظم مع التوثيق", "إنشاء اختبارات شاملة", "مراجعة الكود والتأكد من الجودة", "تحديث ملفات التتبع والتوثيق"], "qualityStandards": {"codeDocumentation": "100%", "testCoverage": "90%+", "codeQuality": "8/10+", "errorTolerance": "0"}}}}