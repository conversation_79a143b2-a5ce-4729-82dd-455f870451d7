# AI Project Creator Script
# Creates a new AI-powered project with all configurations

param(
    [Parameter(Mandatory = $true)]
    [string]$ProjectName,

    [Parameter(Mandatory = $false)]
    [string]$ProjectPath = "."
)

$SourcePath = $PSScriptRoot
$TargetPath = Join-Path $ProjectPath $ProjectName

Write-Host "🚀 Creating new AI project: $ProjectName" -ForegroundColor Green

# Create project folder
if (!(Test-Path $TargetPath)) {
    New-Item -ItemType Directory -Path $TargetPath -Force | Out-Null
    Write-Host "📁 Created project folder: $TargetPath" -ForegroundColor Yellow
}

# Copy VS Code settings
$VsCodeSource = Join-Path $SourcePath ".vscode"
$VsCodeTarget = Join-Path $TargetPath ".vscode"

if (Test-Path $VsCodeSource) {
    Copy-Item -Path $VsCodeSource -Destination $VsCodeTarget -Recurse -Force
    Write-Host "⚙️ Copied VS Code settings" -ForegroundColor Yellow
}

# Copy Continue AI settings
$ContinueSource = Join-Path $SourcePath ".continue"
$ContinueTarget = Join-Path $TargetPath ".continue"

if (Test-Path $ContinueSource) {
    Copy-Item -Path $ContinueSource -Destination $ContinueTarget -Recurse -Force
    Write-Host "🤖 Copied Continue AI settings" -ForegroundColor Yellow
}

# Copy AI Agent Guide
$GuideSource = Join-Path $SourcePath "AI-AGENT-GUIDE.md"
$GuideTarget = Join-Path $TargetPath "AI-AGENT-GUIDE.md"

if (Test-Path $GuideSource) {
    Copy-Item -Path $GuideSource -Destination $GuideTarget -Force
    Write-Host "📚 Copied AI Agent Guide" -ForegroundColor Yellow
}

# Create minimal structure - let AI agent decide the rest
$Folders = @("docs")
foreach ($Folder in $Folders) {
    $FolderPath = Join-Path $TargetPath $Folder
    if (!(Test-Path $FolderPath)) {
        New-Item -ItemType Directory -Path $FolderPath -Force | Out-Null
    }
}
Write-Host "📁 Created minimal structure (AI will create the rest)" -ForegroundColor Yellow

# Create simple PROJECT-CONTEXT.md
$ContextContent = "# AI Project Context: $ProjectName`n`nProject Name: $ProjectName`nCreated: $(Get-Date -Format 'yyyy-MM-dd HH:mm')`nStatus: Ready for AI Agent`n`nInstructions for AI Agent:`nThis is a new project. Please analyze the project type and create appropriate structure.`n`nTasks:`n1. Detect project type`n2. Create folder structure`n3. Set up development environment`n4. Begin development`n`nLast Updated: $(Get-Date -Format 'yyyy-MM-dd HH:mm')"

Set-Content -Path (Join-Path $TargetPath "PROJECT-CONTEXT.md") -Value $ContextContent -Encoding UTF8

# Create minimal README.md
$ReadmeContent = @"
# $ProjectName

## 🤖 AI-Powered Project
This project is set up to work seamlessly with AI agents.

## 📋 Getting Started
1. Open this project in VS Code
2. Press Ctrl+Shift+C to open Continue AI
3. Tell the AI agent what you want to build
4. The AI will detect project type and set up everything automatically

## 📚 For AI Agent
- Read `PROJECT-CONTEXT.md` for complete project information
- Read `AI-AGENT-GUIDE.md` for development guidelines
- Follow the universal workflow for any project type

## 🎯 Project Status
- **Status:** Ready for AI development
- **Next Step:** Tell the AI what you want to build

---
*This project uses Universal AI Project Foundation*
"@

Set-Content -Path (Join-Path $TargetPath "README.md") -Value $ReadmeContent -Encoding UTF8

# Create .gitignore file
$GitignoreContent = @"
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Testing
.coverage
.pytest_cache/
htmlcov/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Local History
.history/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
"@

Set-Content -Path (Join-Path $TargetPath ".gitignore") -Value $GitignoreContent -Encoding UTF8

Write-Host ""
Write-Host "✅ Project created successfully!" -ForegroundColor Green
Write-Host "📂 Project path: $TargetPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎯 Next steps:" -ForegroundColor Yellow
Write-Host "1. Open project in VS Code: code `"$TargetPath`"" -ForegroundColor White
Write-Host "2. Install recommended extensions when prompted" -ForegroundColor White
Write-Host "3. Enter API Key in Continue settings" -ForegroundColor White
Write-Host "4. Start working with the AI agent!" -ForegroundColor White
Write-Host ""
Write-Host "🤖 To start with the agent:" -ForegroundColor Cyan
Write-Host "Press Ctrl+Shift+C to open Continue AI" -ForegroundColor White
